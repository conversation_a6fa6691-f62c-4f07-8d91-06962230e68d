# 组件目录

## 注意事项

1. 目录结构

- 目录命名：新组件必须创建独立的目录，目录名称必须遵循大写字母开头的驼峰命名法，例如 `DemoComponent`。
- 文件命名：目录下的组件文件统一命名为 `index.jsx`，组件代码必须都在`index.jsx`中，以保持目录的整洁和一致性。

2. 技术栈与组件结构

- React 版本：使用 React 技术栈，版本为 `React 18.2`。
- 组件类型：优先使用函数式组件，不能使用 TypeScript，不要使用 React.memo。
- 命名规范：组件名称必须以大写字母开头，并遵循驼峰命名法。
- HTML 处理：使用面向 SEO 友好、有语义的 HTML 标签。
- 样式处理：
  - 使用 TailwindCSS 实现样式，或采用内联 css style 样式，涉及逻辑判断的类名提前处理。不要设置字体，字体大小使用 px 为单位。
  - “涨跌平”对应的字体颜色或者背景色，直接添加对应的 style 样式，上涨使用 color-price-up 变量，下跌使用 color-price-down，平使用 color-price-even。
  - 确保样式支持支持移动端正常展示、支持响应式设计，样式适配不同设备和屏幕尺寸。比如最外层元素宽度不要限制固定宽度，可以用响应式单位。
- 文本处理：组件涉及的静态文本不要写死，可通过 props 配置并设置默认值。
- 导出规范：组件必须设置为默认导出，以便于在项目中统一引用。
- props 设计：组件的 props 参数名必须具有语义化，避免使用 id、data、value、type 等模糊不清的命名。
  - 需要暴露 props 属性，以提供给编辑器分析组件入参信息，如：

```js
// 组件其他代码...
export const props = {
  // props属性
  text: {
    type: 'string', // 属性类型，boolean|string|number|object|array|date
    default: 'button', // 默认值，非必需
  },
};
// 组件其他代码...
```

- props 及数据处理：做好兜底和容错处理，比如数组遍历前要进行空值判断。

- \*组件拆分标准规则
  - 组件间涉及联动、数据状态共享，组件合放在一块、不做组件/文件拆分
  - 有“重复“内容、存在可”抽象“且能复用的组件，可以单独拆分出组件

3. 依赖管理

- 外部依赖：组件内部暂时不引入除 React 外的其他依赖，包括 CSS 文件。
- 数据与请求：
  - 数据信息通过 props 导入，暂时不要在组件中进行请求处理。
  - 请求处理使用 `window.axios` 方法，功能与 `axios` 相同。
- 日期处理：使用 `window.dayjs` 方法处理日期，功能与 `dayjs` 相同。
- 提示反馈：使用 `window.globalToast` 方法进行 toast 提示，参数为信息字符串。

4. 代码规范

- 模板字符串：避免使用 `${}` 结构的模板字符串语法，都使用加号进行字符串拼接，包括动态 className。
- 注释说明：组件必须包含完备的注释，说明以下内容：组件名称，组件功能，技术栈要求，适用场景，使用示例等
- 代码风格：遵循业界优秀的 JavaScript 代码风格指南，保持代码的一致性和可读性

5. 调试与预览

- 调试：组件生成后，在 `Preview.jsx`（src/components） 组件中进行调试，确保组件功能正常。
