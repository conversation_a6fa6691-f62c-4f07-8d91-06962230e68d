/*
Prettier配置文件，用于统一代码格式化规则。该文件定义了项目的代码样式规范，包括行宽、缩进、引号等格式化选项。
*/
/*
文件功能概述：
- 这是一个Prettier配置文件，用于定义项目的代码格式化规则
- 通过module.exports导出配置对象，供Prettier格式化工具使用

配置项说明：
- printWidth: 设置每行代码的最大字符数为100
- tabWidth: 设置缩进为2个空格
- useTabs: 使用空格而非tab进行缩进
- singleQuote: 使用单引号替代双引号
- semi: 在语句末尾添加分号
- trailingComma: 在ES5中支持的多行结构中添加尾随逗号
- bracketSpacing: 在对象字面量的括号内添加空格
- arrowParens: 箭头函数的参数使用简单格式(单个参数时省略括号)

使用说明：
1. 将此文件放置在项目根目录
2. 安装prettier依赖: npm install --save-dev prettier
3. 在编辑器中配置使用prettier格式化
4. 可以通过npm scripts添加格式化命令：
   "format": "prettier --write \"src/**/
// document: https://prettier.io/docs/en/configuration.html

module.exports = {
  printWidth: 100,
  tabWidth: 2,
  useTabs: false,
  singleQuote: true,
  semi: true,
  trailingComma: 'es5',
  bracketSpacing: true,
  arrowParens: 'avoid',
};
