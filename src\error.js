/**
 * @typedef {Object} TradeErrorCodes
 * @property {'AUTH_FAILED'} AUTH_FAILED - Authentication failed error
 * @property {'NETWORK_ERROR'} NETWORK_ERROR - Network related error
 * @property {'REQUEST_TIMEOUT'} REQUEST_TIMEOUT - Request timeout
 * @property {'SERVER_ERROR'} SERVER_ERROR - Server side error
 * @property {'UNKNOWN_ERROR'} UNKNOWN_ERROR - Unknown error type
 */

/**
 * Custom error class for Trade API errors
 * @class
 * @extends Error
 */
export class TradeError extends Error {
  /**
   * @param {string} code - Error code from TradeErrorCodes
   * @param {string} message - Error message
   * @param {Error|null} [originalError=null] - Original error object
   */
  constructor(code, message, originalError = null) {
    super(message);
    this.name = 'TradeError';
    this.code = code;
    this.message = message;
    this.originalError = originalError;
  }
}

export const TradeErrorCodes = Object.freeze({
  BAD_REQUEST: 'BAD_REQUEST',
  AUTH_FAILED: 'AUTH_FAILED',
  NETWORK_ERROR: 'NETWORK_ERROR',
  REQUEST_TIMEOUT: 'REQUEST_TIMEOUT',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
});
