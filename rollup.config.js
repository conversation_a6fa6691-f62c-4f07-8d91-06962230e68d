import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import typescript from '@rollup/plugin-typescript';
import { terser } from 'rollup-plugin-terser';

export default {
  input: 'src/index.js',
  output: [
    {
      file: 'dist/index.js',
      format: 'cjs',
      sourcemap: true,
      // 添加 globals 配置
      globals: {
        axios: 'axios',
        '@ainvest/event-bus': 'thsc_ainvest_event_bus',
      },
    },
    {
      file: 'dist/index.esm.js',
      format: 'es',
      sourcemap: true,
      // 添加 globals 配置
      globals: {
        axios: 'axios',
        '@ainvest/event-bus': 'thsc_ainvest_event_bus',
      },
    },
  ],
  external: ['axios', '@ainvest/event-bus'],
  plugins: [
    typescript({
      tsconfig: './tsconfig.json',
      declaration: true,
      declarationDir: './dist',
      rootDir: './src',
      allowJs: true, // 允许编译 JS 文件
    }),
    resolve(),
    commonjs(),
    terser(),
  ],
};
