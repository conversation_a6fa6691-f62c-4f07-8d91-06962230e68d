import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path'
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  base: './',
  plugins: [react()],
  alias: {
    "@": resolve(__dirname, "./src"),
  },
  // server: {
  //   proxy: {
  //     '/api': {
  //       target: 'https://ainvest-trade.touzime.com',
  //       changeOrigin: true,
  //       secure: false, // 如果是https接口，需要配置这个参数
  //       configure: (proxy, _options) => {
  //         proxy.on('proxyReq', (proxyReq, req, _res) => {
  //           // Copy cookies from the incoming request to the proxy request
  //           const cookie = req.headers.cookie;
  //           console.log('cookie->',cookie);
  //           if (cookie) {
  //             proxyReq.setHeader('Cookie', cookie);
  //           }
  //         });
  //       }
  //     }
  //   }
  // },
  build: {
    lib: {
      entry: path.resolve(__dirname, 'src/services/tradeAPIGlobal.ts'),
      name: 'TradeAPIGlobal',
      formats: ['umd'],
      fileName: (format) => `trade-api.${format}.js`
    },
    
  }
});
