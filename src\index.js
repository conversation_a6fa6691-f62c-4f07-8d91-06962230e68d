// CreateTradeAPI.js
import axios from 'axios';
import { TradeError, TradeErrorCodes } from './error';
import { EventBus } from '@ainvest/event-bus';

const API_ENDPOINTS = {
  PRODUCTION: {
    API_URL: 'https://trade.ainvest.com',
    DICT_API_URL: 'https://dict.ainvest.com',
    BROKER_LIST_URL:
      'https://api.ainvest.com/dynamic-configuration/open/api/v1/config_list?key=brokerInfo',
  },
  TESTING: {
    API_URL: 'https://ainvest-trade.touzime.com',
    DICT_API_URL: 'https://ainvest-seek.touzime.com',
    BROKER_LIST_URL:
      'https://ainvest-api.touzime.com/dynamic-configuration/open/api/v1/config_list?key=brokerInfo',
  },
};

const HTTP_STATUS_CODE = {
  BAD_REQUEST: 400,
  AUTH_FAILED: 401,
};

const TRADE_URL = {
  SESSION: '/api/oauth/v1/session',
  PINCODE: '/api/security/v1/pincode',
};

const AUTH_CENTER_ERROR_CODE = {
  TOKEN_INVALID: 106,
  USER_ID_NOT_EXSIT: 108,
  TOKEN_NOT_EXSIT: 109,
};

// 创建 TradeAPI 类
class TradeAPI {
  #pendingRequests = new Map();

  static getInstance() {
    if (!TradeAPI.instance) {
      throw new Error('TradeAPI not initialized. Call init() first');
    }
    return TradeAPI.instance;
  }

  static init(env, prodId, appName, fingerPrint) {
    // 参数验证
    if (!env || !prodId || !appName || !fingerPrint) {
      throw new Error('Missing required parameters for TradeAPI initialization');
    }

    // 如果已经初始化，抛出错误或返回现有实例
    if (TradeAPI.instance) {
      return TradeAPI.instance;
    }

    TradeAPI.instance = new TradeAPI(env, prodId, appName, fingerPrint);
    return TradeAPI.instance;
  }

  // 添加重置方法
  static reset() {
    TradeAPI.instance = null;
  }

  constructor(env, progId, appName, fingerPrint) {
    this.config = {};
    this.isPincodeSet = false;
    this.lastFailedRequestTime = 0;
    this.lastQueryAccountsTime = 0;
    // 500 millseconds timeout
    this.failureTimeout = 500;
    this.isAccountsAvailable = false;
    this.accountsList = [];
    this.brokerList = [];
    this.API_ENDPOINTS = env === 'production' ? API_ENDPOINTS.PRODUCTION : API_ENDPOINTS.TESTING;
    this.tradeAxios = axios.create({
      baseURL: this.API_ENDPOINTS.API_URL,
      withCredentials: true,
      timeout: 10000,
      headers: {
        'X-Auth-ProgId': progId,
        'X-Auth-AppName': appName,
        'X-Auth-Udid': fingerPrint,
      },
    });
  }

  // Add method to check if we should block requests
  shouldBlockRequest() {
    const now = Date.now();
    if (now - this.lastFailedRequestTime < this.failureTimeout) {
      return true;
    }
    return false;
  }

  // Modify API methods to check for blocked state
  async makeRequest(key, waitPending, apiCall) {
    if (this.shouldBlockRequest()) {
      throw new Error('Request blocked due to recent authentication failure');
    }

    const requestKey = key || '';

    // 只有查询类请求（waitPending = true）才检查和复用正在进行的请求
    if (waitPending && requestKey.length > 0 && this.#pendingRequests.has(requestKey)) {
      return this.#pendingRequests.get(requestKey);
    }

    const promise = apiCall().finally(() => {
      this.#pendingRequests.delete(requestKey);
    });

    // 只有查询类请求才存储到 pendingRequests
    if (waitPending) {
      this.#pendingRequests.set(requestKey, promise);
    }

    return promise;
  }

  // 获取券商列表
  async getBrokerList() {
    if (this.brokerList.length > 0) {
      return this.brokerList;
    }
    return this.makeRequest('getBrokerList', true, async () => {
      try {
        const response = await this.tradeAxios.get(this.API_ENDPOINTS.BROKER_LIST_URL);
        if (response.data.status_code === 0) {
          this.brokerList = response.data.data.brokerInfo;
          return response.data.data.brokerInfo;
        }
        return [];
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  //获取tsid
  async getTSID() {
    return this.makeRequest('getTSID', true, async () => {
      try {
        const response = await this.tradeAxios.get(TRADE_URL.SESSION);
        return response.data.d || '';
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  //创建tsid
  async createTSID() {
    return this.makeRequest('createTSID', true, async () => {
      try {
        const response = await this.tradeAxios.post(TRADE_URL.SESSION);
        return response.data.d;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  //获取授权连接
  async getOAuthLink(institutionTag, state) {
    return this.makeRequest('getOAuthLink', true, async () => {
      try {
        const response = await this.tradeAxios.get(
          `/api/oauth/v1/link/${institutionTag}?state=${state}`
        );
        return response.data.d;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  //获取Token
  async getOAuthToken(code, state) {
    return this.makeRequest('getOAuthToken', true, async () => {
      try {
        const response = await this.tradeAxios.post(
          `/api/oauth/v1/token?code=${code}&state=${state}`
        );
        return response.data.d;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  //获取券商配置
  async getConfig(institutionTag) {
    if (this.config[institutionTag]) {
      return this.config[institutionTag];
    }
    return this.makeRequest('getConfig', true, async () => {
      try {
        const response = await this.tradeAxios.get(`/api/${institutionTag}/v1/config`);
        this.config[institutionTag] = response.data.d;
        return this.config[institutionTag];
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  //查询是否设置了pincode
  async getIsPincodeSet() {
    if (this.isPincodeSet) {
      return true;
    }
    try {
      const response = await this.tradeAxios.get(TRADE_URL.PINCODE);
      this.isPincodeSet = response.data.d;
      return this.isPincodeSet;
    } catch (error) {
      this.handleError(error);
    }
  }

  //首次设置pincode
  async initPincode(pincode) {
    return this.makeRequest('initPincode', true, async () => {
      try {
        const response = await this.tradeAxios.post(TRADE_URL.PINCODE, {
          pinCode: pincode,
        });
        this.isPincodeSet = response.data.d;
        return this.isPincodeSet;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  //忘记密码第一步-发送验证码
  async sendSMSCode() {
    return this.makeRequest('send SMS code', true, async () => {
      try {
        const response = await this.tradeAxios.get('/api/security/v1/verificationcode');
        return response.data.d;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  //忘记密码第二步-验证验证码
  async verifySMSCode(smsCode) {
    return this.makeRequest('verify SMS code', true, async () => {
      try {
        const response = await this.tradeAxios.post('/api/security/v1/verificationcode', {
          verificationCode: smsCode,
        });
        return response.data.d;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  //忘记密码第三步-更新密码
  async updatePincodeBySMSCode(newPincode, oneTimeToken) {
    return this.makeRequest('update pincode', true, async () => {
      try {
        const response = await this.tradeAxios.put(TRADE_URL.PINCODE, {
          updateType: 'RESET',
          newPinCode: newPincode,
          oneTimeToken: oneTimeToken,
        });
        return response.data.d;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  //用旧密码更新密码
  async updatePincodeByOldPincode(oldPincode, newPincode) {
    return this.makeRequest('update pincode', true, async () => {
      try {
        const response = await this.tradeAxios.put(TRADE_URL.PINCODE, {
          updateType: 'OVERWRITE',
          newPinCode: newPincode,
          oldPinCode: oldPincode,
        });
        return response.data.d;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  //校验pincode
  async checkPincode(institutionTag, accountId, pincode, refreshType) {
    try {
      const institutionTagSent = refreshType === 'refresh_token' ? institutionTag : null;
      const accountIdSent = refreshType === 'refresh_token' ? accountId : null;
      await this.tradeAxios.put(TRADE_URL.SESSION, {
        institutionTag: institutionTagSent,
        accountId: accountIdSent,
        refreshType,
        pinCode: pincode,
      });
      // Reset the failure timer
      this.lastFailedRequestTime = 0;
      return true;
    } catch (error) {
      this.handleError(error);
    }
  }

  //刷新tsid
  async refreshSession() {
    try {
      await this.tradeAxios.post(TRADE_URL.SESSION, {
        refreshType: 'renew',
      });
      return true;
    } catch (error) {
      throw error;
    }
  }

  // 获取账户信息
  async getBindingAccounts() {
    return this.makeRequest('getBindingAccounts', true, async () => {
      try {
        const response = await this.tradeAxios.get('/api/aggregate/v1/accounts');
        if (response.data.d.length > 0 && !this.isAccountsAvailable) {
          this.isAccountsAvailable = true;
        }
        return response.data.d;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  //查询是否有绑定的账号
  async hasBindingAccounts() {
    if (this.isAccountsAvailable) {
      return true;
    }
    try {
      await this.getBindingAccounts();
    } catch (error) {
      return false;
    }

    return this.isAccountsAvailable;
  }

  //添加绑定账号
  async addAccounts(accountList) {
    return this.makeRequest('addAccounts', true, async () => {
      try {
        const response = await this.tradeAxios.post('/api/aggregate/v1/accounts', accountList);
        return response.data.d;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  //获取账号信息
  async getAccount(institutionTag, accessToken) {
    return this.makeRequest('getAccount', true, async () => {
      try {
        const response = await this.tradeAxios.get(`/api/${institutionTag}/v1/accounts`, {
          headers: {
            tradeToken: accessToken,
          },
        });
        return response.data.d;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  // 获取资产信息
  async getState(institutionTag, accountId) {
    return this.makeRequest('getState', true, async () => {
      try {
        const response = await this.tradeAxios.get(
          `/api/${institutionTag}/v1/accounts/${accountId}/state`
        );
        return response.data.d;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  // 获取资产信息更新
  async getStreamState(institutionTag, accountId) {
    return this.makeRequest('getStreamState', true, async () => {
      try {
        const response = await this.tradeAxios.get(
          `/api/${institutionTag}/v1/accounts/${accountId}/stream/state`
        );
        return response.data.d;
      } catch (error) {
        window.console.error(error); //定时请求不抛错误，不然一次不稳定会导致界面直接显示重新登陆
      }
    });
  }

  // 获取持仓信息
  async getPositions(institutionTag, accountId) {
    return this.makeRequest('getPositions', true, async () => {
      try {
        const response = await this.tradeAxios.get(
          `/api/${institutionTag}/v1/accounts/${accountId}/positions`
        );
        return response.data.d;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  // 获取持仓信息更新
  async getStreamPositions(institutionTag, accountId) {
    return this.makeRequest('getStreamPositions', true, async () => {
      try {
        const response = await this.tradeAxios.get(
          `/api/${institutionTag}/v1/accounts/${accountId}/stream/positions`
        );
        return response.data.d;
      } catch (error) {
        window.console.error(error);
      }
    });
  }

  // 获取订单信息
  async getOrders(institutionTag, accountId) {
    return this.makeRequest('getOrders', true, async () => {
      try {
        const response = await this.tradeAxios.get(
          `/api/${institutionTag}/v1/accounts/${accountId}/orders`
        );
        return response.data.d;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  // 获取订单信息
  async getOrdersHistory(
    institutionTag,
    accountId,
    startTime,
    endTime,
    instrument = '',
    status = '',
    side = '',
    securityType = ''
  ) {
    return this.makeRequest('getOrdersHistory', true, async () => {
      try {
        let url = `/api/${institutionTag}/v1/accounts/${accountId}/ordersHistory?startTime=${startTime}&endTime=${endTime}`;
        if (instrument) {
          url += `&instrument=${instrument}`;
        }
        if (status) {
          url += `&status=${status}`;
        }
        if (side) {
          url += `&side=${side}`;
        }
        if (securityType) {
          url += `&securityType=${securityType}`;
        }
        const response = await this.tradeAxios.get(url);
        return response.data.d;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  async getAvailableInfo(institutionTag, accountId, { instrument, side, type, limitPrice }) {
    return this.makeRequest('getAvailableInfo', false, async () => {
      try {
        let url = `/api/${institutionTag}/v1/accounts/${accountId}/available?instrument=${instrument}&side=${side}&type=${type}`;
        if (limitPrice) {
          url += `&limitPrice=${limitPrice}`;
        }
        const response = await this.tradeAxios.get(url);
        return response.data.d;
      } catch (error) {
        //查可买请求短期可能触发多次，不抛错误，不然一次不稳定会导致界面直接显示重新登陆
        window.console.error(error);
      }
    });
  }

  async getSymbolSuggestions(pattern) {
    return this.makeRequest(`getSymbolSuggestions`, false, async () => {
      try {
        const response = await this.tradeAxios.get(
          `${this.API_ENDPOINTS.DICT_API_URL}/stocks?app=us&pattern=${pattern}&pl=w&version=1.0`
        );

        if (response.data.status_code === 0 && response.data.data?.data_info) {
          return response.data.data.data_info;
        } else {
          return [];
        }
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  async previewOrder(
    institutionTag,
    accountId,
    {
      id,
      instrument,
      qty,
      amount,
      quantityType,
      side,
      type,
      limitPrice,
      stopPrice,
      durationType,
      extendHours,
      category,
    }
  ) {
    return this.makeRequest('previewOrder', false, async () => {
      try {
        const response = await this.tradeAxios.post(
          `/api/${institutionTag}/v1/accounts/${accountId}/previewOrder`,
          {
            id,
            instrument,
            qty,
            amount,
            quantityType,
            side,
            type,
            limitPrice,
            stopPrice,
            durationType,
            extendHours,
            category,
          }
        );
        return response.data.d;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  // 模拟下单
  async placeOrder(
    institutionTag,
    accountId,
    requestId,
    {
      instrument,
      qty,
      amount,
      quantityType,
      side,
      type,
      limitPrice,
      stopPrice,
      durationType,
      extendHours,
      category,
      confirmId,
    }
  ) {
    return this.makeRequest('placeOrder', false, async () => {
      try {
        const response = await this.tradeAxios.post(
          `/api/${institutionTag}/v1/accounts/${accountId}/orders?requestId=${requestId}`,
          {
            instrument,
            qty,
            amount,
            quantityType,
            side,
            type,
            limitPrice,
            stopPrice,
            durationType,
            extendHours,
            category,
            confirmId,
          }
        );
        return response.data.d;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  // 撤单
  async cancelOrder(institutionTag, accountId, orderId, category) {
    return this.makeRequest('cancelOrder', false, async () => {
      try {
        const response = await this.tradeAxios.delete(
          `/api/${institutionTag}/v1/accounts/${accountId}/orders/${orderId}`,
          {
            data: { category },
          }
        );
        return response.data;
      } catch (error) {
        this.handleError(error);
      }
    });
  }

  handleAuthFailed(error) {
    this.lastFailedRequestTime = Date.now();
    const errhandle = error.response.data.errhdl;
    if (Array.isArray(errhandle)) {
      // Handle reauth next if it exists
      if (errhandle.includes('clear_tsid') || errhandle.includes('reauth')) {
        EventBus.emit('TRADE::ON_SHOW_AUTH_ENTRY', {});
      }

      // Handle pincode last
      if (errhandle.includes('pincode')) {
        EventBus.emit('TRADE::ON_SHOW_PINCODE', { type: 'pincode' });
      } else if (errhandle.includes('refresh_token')) {
        EventBus.emit('TRADE::ON_SHOW_PINCODE', { type: 'refresh_token' });
      }
    } else {
      const response_code = error.response.data.status_code;
      if (
        response_code &&
        (response_code === AUTH_CENTER_ERROR_CODE.TOKEN_INVALID ||
          response_code === AUTH_CENTER_ERROR_CODE.USER_ID_NOT_EXSIT ||
          response_code === AUTH_CENTER_ERROR_CODE.TOKEN_NOT_EXSIT)
      ) {
        EventBus.emit('TRADE::ON_SHOW_LOGIN_DIALOG', {});
      }
    }
    throw new TradeError(TradeErrorCodes.AUTH_FAILED, 'Authentication failed', error);
  }

  // 统一错误处理
  handleError(error) {
    if (error.code === 'ECONNABORTED') {
      throw new TradeError(TradeErrorCodes.REQUEST_TIMEOUT, 'Request timeout', error);
    } else if (error.response) {
      // 服务器响应了错误状态码
      switch (error.response.status) {
        case HTTP_STATUS_CODE.AUTH_FAILED:
          this.handleAuthFailed(error);
          break;
        default:
          let message = `Request failed with status: ${status}`;
          if (error.response.data.errmsg && typeof error.response.data.errmsg === 'string') {
            message = error.response.data.errmsg;
          }
          throw new TradeError(TradeErrorCodes.SERVER_ERROR, message, error);
      }
    } else if (error.request) {
      throw new TradeError(
        TradeErrorCodes.NETWORK_ERROR,
        'No response received from server',
        error
      );
    } else {
      // 在设置请求时发生了一些事情
      throw new TradeError(TradeErrorCodes.UNKNOWN_ERROR, 'Error in setting up the request', error);
    }
  }
}

// 静态属性保存实例
TradeAPI.instance = null;

export { TradeAPI };
export { TradeError, TradeErrorCodes } from './error';
export default TradeAPI;
