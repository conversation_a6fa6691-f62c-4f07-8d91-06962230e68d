{"name": "@overseafront/trade-api", "version": "0.5.1", "description": "Trade API wrapper for financial transactions", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "rollup -c", "prepare": "npm run build", "test": "jest", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts", "prebuild": "npm run clean"}, "peerDependencies": {"@ainvest/event-bus": "0.0.1", "axios": "^1.x", "js-cookie": "^3.x"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-typescript": "^11.0.0", "@types/jest": "^29.x", "@types/js-cookie": "^3.x", "axios": "^1.x", "jest": "^29.x", "js-cookie": "^3.x", "rimraf": "^5.x", "rollup": "^2.79.1", "rollup-plugin-terser": "^7.x", "tslib": "^2.8.1", "typescript": "^5.x"}, "keywords": ["trade", "api", "financial"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/yourusername/trading-components.git"}, "bugs": {"url": "https://github.com/yourusername/trading-components/issues"}, "homepage": "https://github.com/yourusername/trading-components#readme"}