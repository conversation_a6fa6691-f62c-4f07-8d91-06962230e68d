{
  "compilerOptions": {
    "target": "es2018",
    "module": "esnext",
    "lib": ["es2018", "dom"],
    "typeRoots": ["./node_modules/@types", "./src/types"],
    "declaration": true,
    "declarationDir": "dist",
    "strict": true,
    "moduleResolution": "node",
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "outDir": "dist",
    "rootDir": "src",
    "allowJs": true,          // 允许编译 JS 文件
    "checkJs": true          // 对 JS 文件进行类型检查
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}