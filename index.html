<!--
 * <AUTHOR>
 * @Date 2024-07-02 10:35:32
 * @LastEditTime 2024-12-25 14:53:36
-->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta charset="UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-touch-fullscreen" content="YES" />
    <title>KAmis Create Components App</title>
  </head>
  <body>
    <div id="root"></div>
    <!-- 1. 先加载依赖 -->
    <script src="https://cdn.ainvest.com/frontResources/offline/js/vue/vue-2.7.16.min.js"></script>
    <script src="https://cdn.ainvest.com/frontResources/static/regular/kamis-container/kamis-2c-sdk-umd-register.1.3.8.js"></script>
    <script src="https://ainvest-cdn.touzime.com/frontResources/lib/rsa/jsencrypt.min.js"></script>
    <script src="https://ainvest-cdn.touzime.com/frontResources/lib/rsa/pkey.js"></script>
    <script src="https://cdn.ainvest.com/frontResources/lib/event-store/v0.0.1.js"></script>
    <script src="./dist/trade-api.umd.js"></script>
    <!-- <script src="https://ainvest-cdn.touzime.com/frontResources/lib/trade/trade-api-v0.0.1.js"></script> -->
    <!-- 2. 加载你的组件库并初始化 -->
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
